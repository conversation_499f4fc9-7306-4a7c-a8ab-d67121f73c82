.resource-form-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px 15px;
}

// Loading overlay styles
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

// .card {
//   transition: transform 0.2s;
//   border-radius: 8px;

//   &:hover {
//     transform: translateY(-2px);
//   }
// }
.p-dropdown {
  width: 100% !important;
  height: 39px !important;
  border: 1px solid #ced4da !important;
  border-radius: 0.25rem !important;

  &:not(.p-disabled):hover {
    border-color: #28a745 !important;
  }

  &.p-focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
  }

  .p-dropdown-label {
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
  }

  .p-dropdown-trigger {
    width: 3rem;
  }

  .p-dropdown-panel {
    width: 100%;
  }
  .p-select-label {
    padding-top: 5px;
    padding-left: 8px;
  }
  .p-placeholder {
    padding-top: 5px;
  }
}

.form-label {
  font-weight: 500;
  color: #333;
  position: relative;

  .text-danger {
    display: inline-block;
    font-size: 1rem;
    margin-left: 2px;
    line-height: 1;
    position: relative;
    top: 2px;
  }
}

// Helper text styling
small.text-muted {
  font-size: 0.8rem;
  color: #6c757d;

  &.mb-1 {
    margin-bottom: 0.3rem !important;
  }
}

// Required field indicator
.required-indicator {
  color: #dc3545;
  font-weight: bold;
  margin-left: 2px;
  display: inline-block;
}

// Error messages
.text-danger:not(.form-label .text-danger) {
  color: #dc3545 !important;
  font-size: 0.85rem;
  display: block;
  margin-top: 5px;

  .bi-exclamation-circle {
    font-size: 0.9rem;
  }
}

.upload-container {
  background-color: #f8f9fa;
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #e9ecef;
  }
}

.image-preview img {
  object-fit: cover;
  border-radius: 4px;
}

.form-control,
.form-select {
  border-color: #ced4da;

  &:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
  }

  &.is-invalid {
    border-color: #dc3545;
    background-image: none;
    padding-right: 0.75rem;
  }
}

.form-check-input:checked {
  background-color: #dc3545;
  border-color: #dc3545;
}

/* Custom styles for services inputs */
[formArrayName="services"] {
  .form-control {
    border-width: 0 0 1px 0;
    border-color: #dc3545;
    border-radius: 0;
    padding-left: 0;
    padding-right: 30px;

    &:focus {
      box-shadow: none;
      border-color: #dc3545;
    }
  }
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;

  &:hover {
    background-color: #c82333;
    border-color: #bd2130;
  }
}

.btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;

  &:hover {
    background-color: #dc3545;
    color: white;
  }
}

// Social media icons and inputs
.bi-facebook {
  color: #3b5998;
}

.bi-twitter {
  color: #1da1f2;
}

.bi-instagram {
  color: #e1306c;
}

.bi-linkedin {
  color: #0077b5;
}

// Social media input styling
.input-group {
  .input-group-text {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-right: 0;
    height: 39px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 45px;

    &.is-invalid {
      border-color: #dc3545;
    }
  }
  .form-control.is-invalid {
    z-index: 0;

    & + .invalid-feedback {
      display: block;
    }
  }

  &:has(.form-control.is-invalid) {
    .input-group-text {
      border-color: #dc3545;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .form-check-inline {
    display: block;
    margin-bottom: 10px;
  }
}

// Global PrimeNG component styling for consistent 39px height
:host ::ng-deep {
  // Ensure consistent 39px height for all input fields
  .p-inputtext {
    height: 39px !important;
    padding: 0.375rem 0.75rem !important;
    font-size: 1rem !important;
    border: 1px solid #ced4da !important;
    border-radius: 0.25rem !important;

    &:focus {
      border-color: #28a745 !important;
      box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
    }
  }

  // Dropdown styling
  .p-dropdown {
    height: 39px !important;
    width: 100% !important;

    .p-dropdown-label {
      padding: 0.375rem 0.75rem !important;
      font-size: 1rem !important;
      line-height: 1.5 !important;
      display: flex !important;
      align-items: center !important;
    }

    .p-dropdown-trigger {
      width: 3rem !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }

  // Social media input group styling
  .input-group {
    .input-group-text {
      height: 39px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      border: 1px solid #ced4da !important;
      background-color: #f8f9fa !important;
      min-width: 45px !important;
    }

    .p-inputtext {
      height: 39px !important;
      border-left: 0 !important;
      padding: 0.375rem 0.75rem !important;

      &:focus {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
        outline: none !important;
      }
    }

    // Ensure the input group maintains proper structure
    &:focus-within {
      .input-group-text {
        border-color: #28a745 !important;
      }
    }
  }

  // Radio button alignment
  .p-radiobutton {
    display: inline-flex !important;
    align-items: center !important;
    vertical-align: middle !important;

    .p-radiobutton-box {
      margin-top: 0 !important;
      margin-bottom: 0 !important;
    }
  }
}

// Additional styling for better form appearance
:host ::ng-deep {
  // Ensure textarea fields maintain their multi-row height
  .p-textarea {
    min-height: auto !important;
    padding: 0.375rem 0.75rem !important;
    font-size: 1rem !important;
    border: 1px solid #ced4da !important;
    border-radius: 0.25rem !important;

    &:focus {
      border-color: #28a745 !important;
      box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
    }
  }

  // Services section delete button styling
  .btn-outline-danger {
    border-color: #dc3545 !important;
    color: #dc3545 !important;

    &:hover {
      background-color: #dc3545 !important;
      border-color: #dc3545 !important;
      color: white !important;
    }

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }
  }

  // Standardized button styling to match user list component
  .p-button {
    border-radius: 4px;
    padding: 0.75rem 1.25rem !important;
    height: auto !important;
    min-width: 100px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .p-button-outlined.p-button-danger {
    background-color: white !important;
    color: #dc3545 !important;
    border: 1px solid #dc3545 !important;

    &:hover {
      background-color: rgba(220, 53, 69, 0.04) !important;
      border-color: #c82333 !important;
      color: #c82333 !important;
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
    }
  }

  .p-button-danger:not(.p-button-outlined) {
    background-color: #dc3545 !important;
    color: white !important;
    border: 1px solid #dc3545 !important;

    &:hover {
      background-color: #c82333 !important;
      border-color: #c82333 !important;
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
    }
  }
}

// Toast message styling
:host ::ng-deep {
  .p-toast {
    opacity: 1 !important;

    .p-toast-message {
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      padding: 1rem;
      min-width: 300px;

      .p-toast-summary {
        font-weight: 600;
        font-size: 1.1rem;
      }

      .p-toast-detail {
        margin-top: 6px;
        font-size: 0.95rem;
      }

      &.p-toast-message-success {
        background-color: #e8f5e9;
        border-left: 6px solid #4caf50;
        color: #2e7d32;

        .p-toast-icon {
          color: #2e7d32;
          font-size: 1.5rem;
        }
      }

      &.p-toast-message-error {
        background-color: #ffebee;
        border-left: 6px solid #f44336;
        color: #c62828;

        .p-toast-icon {
          color: #c62828;
          font-size: 1.5rem;
        }
      }

      &.p-toast-message-info {
        background-color: #e3f2fd;
        border-left: 6px solid #2196f3;
        color: #0d47a1;

        .p-toast-icon {
          color: #0d47a1;
          font-size: 1.5rem;
        }
      }
    }
  }
}
